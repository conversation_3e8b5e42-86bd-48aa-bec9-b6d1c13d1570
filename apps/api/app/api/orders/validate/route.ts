import { fetchWithAuthForProxy, withApi<PERSON>eyAuth } from '@/app/lib/auth';
import { type NextRequest, NextResponse } from 'next/server';

const postHandler = async (request: NextRequest) => {
  let body: unknown;
  try {
    body = await request.json();
  } catch (_) {
    return NextResponse.json(
      { error: 'Failed to parse request body' },
      { status: 400 }
    );
  }

  const { data, status } = await fetchWithAuthForProxy(
    '/api/v1/orders/validate',
    {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    },
    request.headers
  );

  return NextResponse.json(data, {
    status,
  });
};

export const OPTIONS = withApiKeyAuth(postHandler);
export const POST = withApi<PERSON>eyAuth(postHandler);
