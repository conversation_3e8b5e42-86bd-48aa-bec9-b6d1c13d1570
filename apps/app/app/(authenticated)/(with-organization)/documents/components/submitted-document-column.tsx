'use client';

import type { SubmittedDocument } from '@/app/types';
import { Badge } from '@repo/design-system/components/ui/badge';
import type { ColumnDef } from '@tanstack/react-table';

// Status badge component
const StatusBadge = ({ status }: { status: SubmittedDocument['status'] }) => {
  let variant:
    | 'default'
    | 'secondary'
    | 'destructive'
    | 'outline'
    | 'success'
    | 'warning' = 'default';

  switch (status) {
    case 'Valid':
      variant = 'success';
      break;
    case 'Invalid':
      variant = 'destructive';
      break;
    case 'Cancelled':
      variant = 'secondary';
      break;
    case 'Submitted':
      variant = 'default';
      break;
    default:
      variant = 'default';
      break;
  }

  return (
    <Badge variant={variant} className="capitalize">
      {status}
    </Badge>
  );
};

export const columns: ColumnDef<SubmittedDocument>[] = [
  {
    accessorKey: 'code',
    header: 'Invoice Number',
  },
  {
    accessorKey: 'documentDetails.supplier.name',
    header: 'Seller',
    cell: ({ row }) => {
      const document = row.original;
      return document.document_details?.supplier?.name || 'N/A';
    },
  },
  {
    accessorKey: 'documentDetails.buyer.name',
    header: 'Buyer',
    cell: ({ row }) => {
      const document = row.original;
      return document.document_details?.buyer?.name || 'N/A';
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => <StatusBadge status={row.original.status} />,
  },
  {
    accessorKey: 'created_at',
    header: 'Submitted At',
    cell: ({ row }) => new Date(row.original.created_at).toLocaleString(),
  },
];
