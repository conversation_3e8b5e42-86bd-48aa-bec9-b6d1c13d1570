// apps/app/app/(authenticated)/(with-organization)/orders/components/submit-button.tsx
'use client';

import { Button } from '@repo/design-system/components/ui/button';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { useRouter } from 'next/navigation';
import { useMemo } from 'react';
import { useSubmitOrder } from '../hooks/useSubmitOrder';

interface SubmitButtonProps {
  status: string;
  order_id: number;
}

export function SubmitButton({ status, order_id }: SubmitButtonProps) {
  const router = useRouter();
  const { submitOrderToMyInvois, isLoading } = useSubmitOrder();

  // Handle form submission
  const handleSubmit = async () => {
    try {
      await submitOrderToMyInvois(order_id);
      toast.success('Order submitted to MyInvois successfully');

      router.push('/orders');
    } catch (error) {
      console.error('Failed to create order:', error);
      toast.error('Failed to create order. Please try again.');
    }
  };

  const statusMessage = useMemo(() => {
    switch (status) {
      case 'Pending':
      case 'Invalid':
      case 'Cancelled':
        return 'Submit to MyInvois';
      case 'Draft':
        return 'Please set the ready status of the order to true before submitting to MyInvois';
      case 'Submitted':
        return 'Order has already been submitted to MyInvois and is pending for validation';
      case 'Valid':
        return 'Order has already been submitted to MyInvois and is valid';
      default:
        return 'Unknown status';
    }
  }, [status]);

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div>
          <Button
            variant="default"
            size="lg"
            type="button"
            onClick={handleSubmit}
            disabled={
              isLoading || !['Pending', 'Invalid', 'Cancelled'].includes(status)
            }
          >
            {isLoading ? 'Submitting...' : 'Submit to MyInvois'}
          </Button>
        </div>
      </TooltipTrigger>
      <TooltipContent>
        <p>{statusMessage}</p>
      </TooltipContent>
    </Tooltip>
  );
}
