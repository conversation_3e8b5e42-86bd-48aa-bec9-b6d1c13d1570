import { Button } from '@repo/design-system/components/ui/button';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useFormContext,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { Switch } from '@repo/design-system/components/ui/switch';
import { PlusCircle, Trash2 } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useState } from 'react';
import type { AllowanceChargeData } from '../../schemas/order-form-schema';

export function AllowanceCharges({
  lineItemIndex,
  addAllowanceCharge,
  removeAllowanceCharge,
}: {
  lineItemIndex: number;
  addAllowanceCharge: (lineItemIndex: number) => void;
  removeAllowanceCharge: (
    lineItemIndex: number,
    allowanceChargeIndex: number
  ) => void;
}) {
  const form = useFormContext();
  const [
    allowanceChargesFieldAmountRateDisplay,
    setAllowanceChargesFieldAmountRateDisplay,
  ] = useState<'both' | 'rate' | 'amount'>('rate');

  return (
    <div className="mt-6">
      <div className="mb-4 flex items-center justify-between">
        <div>
          <h5 className="mb-4 font-medium">Allowance & Charge of the Item</h5>

          <p className="text-muted-foreground text-xs">
            This field is optional, if you wish to make your einvoice more
            detailed you can add allowance and charge applied to the item.
            <br />
            <br />
            <strong>
              Example: An item priced at RM 1000, you have a RM 10 discount for
              it, then the rate of the discount (allowance) would be 10%
            </strong>
            <br />
            <br />
            An allowance charge could be discount amount applied to an item.
            While a charge could be any extra operational cost added to the
            item.
          </p>
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => addAllowanceCharge(lineItemIndex)}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Add New Field
        </Button>
      </div>

      {form
        .watch(`lineItems.${lineItemIndex}.allowanceCharges`)
        .map((_: AllowanceChargeData, allowanceChargeIndex: number) => {
          return (
            <div
              key={allowanceChargeIndex}
              className="mb-4 space-y-2 rounded-md border p-4"
            >
              <div className="mb-4 flex items-center justify-between">
                <h3 className="font-medium text-md">
                  {allowanceChargeIndex + 1} -{' '}
                  {form.watch(
                    `lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.isCharge`
                  )
                    ? 'Charge'
                    : 'Allowance'}
                </h3>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    removeAllowanceCharge(lineItemIndex, allowanceChargeIndex)
                  }
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <h3 className="font-medium text-md">Toggle Allowance/Charge</h3>

                {/* isCharge */}
                <FormField
                  control={form.control}
                  name={`lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.isCharge`}
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel className="mt-2 cursor-pointer font-normal">
                        <AnimatePresence>
                          {form.watch(
                            `lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.isCharge`
                          ) && (
                            <motion.div
                              key="buyer-info"
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3 }}
                            >
                              <p className="mb-2">This is a charge.</p>
                            </motion.div>
                          )}

                          {!form.watch(
                            `lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.isCharge`
                          ) && (
                            <motion.div
                              key="buyer-info"
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3 }}
                            >
                              <p className="mb-2">This is an allowance.</p>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </FormLabel>
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-1">
                {/* Toggle provide which Allowance Rate/Amount/Both */}

                <Select
                  onValueChange={(value) => {
                    setAllowanceChargesFieldAmountRateDisplay(
                      value as 'both' | 'rate' | 'amount'
                    );

                    if (value === 'rate') {
                      form.setValue(
                        `lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.amount`,
                        undefined
                      );
                      form.setValue(
                        `lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.rate`,
                        0
                      );
                    }

                    if (value === 'amount') {
                      form.setValue(
                        `lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.rate`,
                        undefined
                      );
                      form.setValue(
                        `lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.amount`,
                        0
                      );
                    }

                    if (value === 'both') {
                      form.setValue(
                        `lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.amount`,
                        0
                      );
                      form.setValue(
                        `lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.rate`,
                        0
                      );
                    }
                  }}
                  value={allowanceChargesFieldAmountRateDisplay}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Please provide either rate, amount or both field together" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem key={'both'} value={'both'}>
                      Both
                    </SelectItem>
                    <SelectItem key={'rate'} value={'rate'}>
                      Rate
                    </SelectItem>
                    <SelectItem key={'amount'} value={'amount'}>
                      Amount
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-muted-foreground text-xs">
                  Example: When you provide amount: 100, while the product price
                  is 1000, the system will calculate the rate as 10%, when you
                  provide rate: 10%, while the product price is 1000, the system
                  will calculate the amount as 100.
                  <br />
                  Provide both value if you want to make sure the amount and
                  rate are fully under your control.
                </p>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Amount */}
                {(allowanceChargesFieldAmountRateDisplay === 'amount' ||
                  allowanceChargesFieldAmountRateDisplay === 'both') && (
                  <FormField
                    control={form.control}
                    name={`lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.amount`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Amount</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) =>
                              field.onChange(
                                Number.parseFloat(e.target.value) || 0
                              )
                            }
                            value={field.value}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Rate */}
                {(allowanceChargesFieldAmountRateDisplay === 'rate' ||
                  allowanceChargesFieldAmountRateDisplay === 'both') && (
                  <FormField
                    control={form.control}
                    name={`lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.rate`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Rate (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            max="100"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) =>
                              field.onChange(
                                Number.parseFloat(e.target.value) || 0
                              )
                            }
                            value={field.value}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>

              {/* Reason */}
              <FormField
                control={form.control}
                name={`lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.reason`}
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <FormLabel>Reason</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter reason" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          );
        })}
    </div>
  );
}
