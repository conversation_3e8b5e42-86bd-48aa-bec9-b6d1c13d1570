import { But<PERSON> } from "@repo/design-system/components/ui/button";
import {
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useFormContext,
} from "@repo/design-system/components/ui/form";
import { Input } from "@repo/design-system/components/ui/input";
import { PlusCircle, Trash2 } from "lucide-react";
import { useState } from "react";

export function InvoiceLevelAllowanceCharge() {
  const form = useFormContext();
  const [addedAllowance, setAddedAllowance] = useState(false);
  const [addedCharge, setAddedCharge] = useState(false);

  return (
    <div className="mt-6">
      <div>
        <h5 className="mb-4 font-medium">Invoice Allowance & Charge</h5>

        <p className="text-muted-foreground text-xs">
          This field is optional, invoice allowance & charge is the discount/fee
          applied to the invoice after the tax calculation, it will affect the
          final payable amount of the invoice at the very end.
          <br />
          <br />
          <strong>
            Example: After tax calculation the final payable amount is RM 35, if
            the invoice level allowance is RM 10, the final payable amount will
            be RM 35 - RM 10 = RM 25. Conversely, if the invoice level charge is
            RM 10, the final payable amount will be RM 35 + RM 10 = RM 45
          </strong>
        </p>

        {addedAllowance == false && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="mt-4"
            onClick={() => {
              setAddedAllowance(true);
              if (addedAllowance == false) {
                form.setValue("invoiceLevelAllowanceCharge.discount.amount", 0);
                form.setValue(
                  "invoiceLevelAllowanceCharge.discount.reason",
                  ""
                );
              }
            }}
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Invoice Level Allowance
          </Button>
        )}

        {addedCharge == false && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="mt-4"
            onClick={() => {
              setAddedCharge(true);
              if (addedCharge == false) {
                form.setValue("invoiceLevelAllowanceCharge.fee.amount", 0);
                form.setValue("invoiceLevelAllowanceCharge.fee.reason", "");
              }
            }}
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Invoice Level Charge
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 gap-4">
        {addedAllowance && (
          <div>
            <div className="mb-4 mt-4 flex items-center justify-between">
              <h3 className="font-medium text-md">Invoice Level Allowance</h3>

              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => {
                  form.setValue("invoiceLevelAllowanceCharge", undefined);
                  setAddedAllowance(false);
                }}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
            <FormField
              control={form.control}
              name={`invoiceLevelAllowanceCharge.discount.amount`}
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="0.00"
                      type="number"
                      step="0.01"
                      min="0"
                      {...field}
                      onChange={(e) => {
                        field.onChange(
                          e.target.value === ""
                            ? undefined
                            : Number(e.target.value)
                        );
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
            <FormField
              control={form.control}
              name={`invoiceLevelAllowanceCharge.discount.reason`}
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Reason</FormLabel>
                  <FormControl>
                    <Input placeholder="reason" {...field} />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </div>
        )}

        {addedCharge && (
          <div>
            <div className="mb-4 mt-4 flex items-center justify-between">
              <h3 className="font-medium text-md">Invoice Level Charge</h3>

              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => {
                  form.setValue("invoiceLevelAllowanceCharge", undefined);
                  setAddedCharge(false);
                }}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
            <FormField
              control={form.control}
              name={`invoiceLevelAllowanceCharge.fee.amount`}
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="0.00"
                      type="number"
                      step="0.01"
                      min="0"
                      {...field}
                      onChange={(e) => {
                        field.onChange(
                          e.target.value === ""
                            ? undefined
                            : Number(e.target.value)
                        );
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
            <FormField
              control={form.control}
              name={`invoiceLevelAllowanceCharge.fee.reason`}
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Reason</FormLabel>
                  <FormControl>
                    <Input placeholder="reason" {...field} />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </div>
        )}
      </div>
    </div>
  );
}
