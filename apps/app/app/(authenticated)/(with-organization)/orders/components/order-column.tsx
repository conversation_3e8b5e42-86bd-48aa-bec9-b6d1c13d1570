'use client';

import type { Order } from '@/app/types';
import { Badge } from '@repo/design-system/components/ui/badge';
import type { ColumnDef } from '@tanstack/react-table';

const StatusBadge = ({
  status,
}: {
  status: 'Valid' | 'Invalid' | 'Cancelled' | 'Submitted' | 'Pending' | 'Draft';
}) => {
  let variant:
    | 'default'
    | 'secondary'
    | 'destructive'
    | 'outline'
    | 'success'
    | 'warning' = 'default';

  switch (status) {
    case 'Valid':
      variant = 'success';
      break;
    case 'Invalid':
      variant = 'destructive';
      break;
    case 'Cancelled':
      variant = 'secondary';
      break;
    case 'Submitted':
      variant = 'default';
      break;
    default:
      variant = 'default';
      break;
  }

  return (
    <Badge variant={variant} className="capitalize">
      {status}
    </Badge>
  );
};

export const columns: ColumnDef<Order>[] = [
  // {
  //   accessorKey: 'id',
  //   header: ({ column }) => {
  //     return (
  //       <Button
  //         variant="ghost"
  //         size="sm"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  //       >
  //         Order ID
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  // },
  {
    accessorKey: 'invoice_code',
    header: 'Invoice Number',
  },
  {
    accessorKey: 'buyer',
    header: 'Customer',
    cell: ({ row }) => row.original.buyer?.name ?? '-',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => StatusBadge({ status: row.original.status }),
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: ({ row }) => new Date(row.original.created_at).toLocaleString(),
  },
];
