import { z } from "zod";

// Import country names from design system data
import countries from "@repo/design-system/data/global_countries.json";

import { classificationCodesService } from "./classifications";
import { PaymentModes } from "./payment-modes";
import { taxTypesService } from "./tax-types";
import { unitTypesService } from "./unit-types";

const COUNTRY_ISO3 = countries.map((country) => country.iso3);

export const basicInfoSchema = z.object({
  isReady: z.boolean(),
  isConsolidate: z.boolean(),

  invoiceCode: z
    .string()
    .min(1, "Invoice code is required")
    .max(255, "Invoice code must be less than 255 characters"),
  invoiceDateTime: z.date(),

  // Buyer information
  buyerName: z.string().optional(),
  buyerTin: z.string().optional(),
  registrationType: z.enum(["BRN", "NRIC", "PASSPORT", "ARMY"]).optional(),
  registrationNumber: z.string().optional(),
  sstRegistrationNumber: z.string().optional(),
  email: z.string().email().optional(),
  address: z
    .object({
      addressLine0: z.string().min(1, "Address line is required"),
      addressLine1: z.string().optional(),
      addressLine2: z.string().optional(),
      postalZone: z.string().optional(),
      cityName: z.string().min(1, "City is required"),
      state: z.string().min(1, "State is required"),
      country: z.string().min(1, "Country is required"),
    })
    .optional()
    .or(z.object({}).optional()),
  contactNumber: z.string().optional(),
});

export const lineItemsSchema = z.object({
  lineItems: z
    .array(
      z.object({
        id: z.string().min(1, "ID is required"),
        classifications: z
          .array(classificationCodesService.getAsEnum())
          .min(1, "At least one classification is required"),
        description: z.string().trim().min(1, "Description is required"),
        unit: z.object({
          price: z.number().min(0, "Price must be a positive number"),
          count: z.number().min(1, "Count must be at least 1"),
          code: unitTypesService.getAsEnum().optional(),
        }),
        taxDetails: z.array(
          z.object({
            taxType: taxTypesService.getAsEnum(),
            taxRate: z
              .object({
                ratePerUnit: z.number().optional(),
                percentage: z.number().optional(),
              })
              .refine(
                (data) =>
                  data.ratePerUnit !== undefined ||
                  data.percentage !== undefined,
                {
                  message:
                    "Either rate per unit or percentage must be provided",
                  path: ["ratePerUnit", "percentage"],
                }
              ),
          })
        ),
        taxExemption: z
          .object({
            taxableAmount: z.number(),
            reason: z.string().trim().min(1, "Reason is required"),
          })
          .optional(),
        allowanceCharges: z
          .array(
            z
              .object({
                amount: z
                  .number()
                  .min(0, "Amount must be a positive number")
                  .optional(),
                rate: z
                  .number()
                  .max(100, "Rate must be less than 0.9")
                  .optional(),
                reason: z.string().trim().min(1, "Reason is required"),
                isCharge: z.boolean(),
              })
              .refine(
                (input) => {
                  if (input.amount === undefined && input.rate === undefined) {
                    return false;
                  }

                  return true;
                },
                {
                  message: "Either amount or rate must be provided",
                }
              )
          )
          .optional(),
        tarriffCode: z.string().optional(),
        originCountry: z.enum(COUNTRY_ISO3 as [string, ...string[]]),
      })
    )
    .min(1, "At least one line item is required"),
  invoiceLevelAllowanceCharge: z
    .object({
      discount: z
        .object({
          amount: z
            .number()
            .min(0, "Amount must be a positive number")
            .optional(),
          reason: z.string().trim().min(1, "Reason is required"),
        })
        .optional(),

      fee: z
        .object({
          amount: z
            .number()
            .min(0, "Amount must be a positive number")
            .optional(),
          reason: z.string().trim().min(1, "Reason is required"),
        })
        .optional(),
    })
    .optional()
    .refine(
      (input) => {
        if (
          input != undefined &&
          input?.discount === undefined &&
          input?.fee === undefined
        ) {
          return false;
        }

        return true;
      },
      {
        message: "Either discount or fee must be provided",
      }
    ),
});

export const additionalDetailsSchema = z.object({
  // Payment information
  payment: z
    .object({
      mode: PaymentModes.getAsEnum(),
      terms: z.string().optional(),
    })
    .optional()
    .or(z.object({}).optional()),

  // Foreign currency information
  foreignCurrency: z
    .object({
      currencyCode: z.string().min(1, "Currency code is required"),
      currencyExchangeRate: z
        .number()
        .min(0, "Exchange rate must be a positive number"),
    })
    .optional()
    .or(z.object({}).optional()),

  // Additional document references
  additionalDocumentReference: z
    .array(
      z.object({
        id: z.string().min(1, "Reference ID is required"),
        type: z
          .enum(["CustomsImportForm", "FreeTradeAgreement", "K2", "CIF"])
          .optional(),
        description: z.string().optional(),
      })
    )
    .optional(),
});

// TODO: not sure how to show in the forms
const invoiceFormSchema = z.object({
  deliveryDetails: z
    .object({
      recipientName: z.string().optional(),
      recipientAddress: z
        .object({
          addressLine0: z.string().min(1, "Address line is required"),
          addressLine1: z.string().optional(),
          addressLine2: z.string().optional(),
          postalZone: z.string().optional(),
          cityName: z.string().min(1, "City is required"),
          state: z.string().min(1, "State is required"),
          country: z.string().min(1, "Country is required"),
        })
        .optional()
        .nullable(),
      recipientTin: z.string().optional(),
      recipientRegistration: z
        .object({
          type: z.enum(["BRN", "NRIC", "PASSPORT", "ARMY"]).optional(),
          number: z.string().optional(),
        })
        .optional(),
      shipmentDetails: z
        .object({
          shipperInternalTrackingId: z.string().optional(),
          amount: z.number().optional(),
          currencyCode: z.string().optional(),
          allowanceChargeReason: z.string().optional(),
        })
        .optional(),
    })
    .optional(),
  billingPeriod: z
    .object({
      frequency: z.string().min(1, "Frequency is required"),
      startDate: z.string().min(1, "Start date is required"),
      endDate: z.string().min(1, "End date is required"),
    })
    .optional(),
  prePayment: z
    .object({
      amount: z.number().min(0, "Amount must be a positive number"),
      date: z.string().min(1, "Date is required"),
      time: z.string().min(1, "Time is required"),
      referenceNumber: z.string().min(1, "Reference number is required"),
    })
    .optional(),
  billingReferenceNumber: z.string().optional(),
});

export const createOrderFormSchema = basicInfoSchema.merge(
  lineItemsSchema.merge(additionalDetailsSchema.merge(invoiceFormSchema))
);

// Export types for use in the application
export type OrderFormData = z.infer<typeof createOrderFormSchema>;
export type LineItemData = OrderFormData["lineItems"][number];
export type TaxDetailData = LineItemData["taxDetails"][number];
export type AllowanceChargeData = NonNullable<
  LineItemData["allowanceCharges"]
>[number];
