'use client';

import { ApiEndpoint } from '@/app/lib/api';
import { useApiSWR } from '@/app/lib/swr';
import type { Company } from '@/app/types';
import { Skeleton } from '@repo/design-system/components/ui/skeleton';
import { urlSerialize } from '@repo/design-system/lib/utils';

export default function CompanyForm() {
  const { data, isLoading, error } = useApiSWR<{ data: Company }>(
    // Use urlSerialize to add parameters to the URL
    urlSerialize(ApiEndpoint.ME_COMPANIES),
    {
      revalidateOnFocus: false,
    }
  );

  if (error) {
    return <div>Failed to load company</div>;
  }

  if (isLoading) {
    return <Skeleton />;
  }

  if (!data?.data) {
    return <div>No company found</div>;
  }

  const company = data.data;

  return <section className="space-y-6"></section>;
}
