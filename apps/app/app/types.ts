// E-Invoice types from backend
export interface Address {
  addressLine0: string;
  addressLine1?: string;
  addressLine2?: string;
  postalZone?: string;
  cityName: string;
  state: string;
  country: string;
}

// Using const assertion instead of enum for better TypeScript practices
export const RegistrationTypes = {
  BRN: 'BRN',
  NRIC: 'NRIC',
  PASSPORT: 'PASSPORT',
  ARMY: 'ARMY',
} as const;

export type RegistrationType =
  (typeof RegistrationTypes)[keyof typeof RegistrationTypes];

export interface Buyer {
  name: string;
  tin: string;
  registrationType?: RegistrationType;
  registrationNumber?: string;
  sstRegistrationNumber?: string;
  email?: string;
  address?: Address;
  contactNumber?: string;
}

export interface Supplier extends Buyer {
  contactNumber: string;
  address: Address;
  tourismTaxRegistrationNumber?: string;
  msic: string;
  businessActivityDescription: string;
  bankAccount?: string;
  exporterCertifiedNumber?: string;
}

export interface DeliveryDetails {
  recipientName?: string;
  recipientAddress?: Address;
  recipientTin?: string;
  recipientRegistration?: {
    type: RegistrationType;
    number: string;
  };
  shipmentDetails?: {
    shipperInternalTrackingId: string;
    amount: number;
    currencyCode: string;
    allowanceChargeReason: string;
  };
}

// Using const assertion instead of enum for better TypeScript practices
export const AdditionalDocumentReferenceTypes = {
  CUSTOM_IMPORT_FORM: 'CustomsImportForm',
  FREE_TRADE_AGREEMENT: 'FreeTradeAgreement',
  K2: 'K2',
  CIF: 'CIF',
} as const;

export type AdditionalDocumentReferenceType =
  (typeof AdditionalDocumentReferenceTypes)[keyof typeof AdditionalDocumentReferenceTypes];

export interface TaxRate {
  ratePerUnit?: number;
  percentage?: number;
}

export interface LineItemAllowanceCharge {
  rate: number;
  amount: number;
  reason: string;
  isCharge: boolean;
}

export interface LineItemTaxDetail {
  taxType: string;
  taxRate: TaxRate;
  taxExemption?: {
    taxableAmount: number;
    taxAmount: number;
    reason: string;
  };
}

export interface LineItem {
  id: string;
  classifications: string[];
  description: string;
  unit: {
    price: number;
    count: number;
    code?: string;
  };
  taxAmount: number;
  taxDetails: LineItemTaxDetail[];
  allowanceCharges?: LineItemAllowanceCharge[];
  tarriffCode?: string;
  originCountry: string;
}

export interface ForeignCurrency {
  currencyCode: string;
  currencyExchangeRate: number;
}

export interface Payment {
  mode: string;
  terms?: string;
}

export interface Prepayment {
  amount: number;
  date: string; // DateTime
  time: string; // DateTime
  referenceNumber: string;
}

export interface InvoiceLevelAllowanceCharge {
  discount?: {
    amount: number;
    reason: string;
  };
  fee?: {
    amount: number;
    reason: string;
  };
}

export interface BillingPeriod {
  frequency: string;
  startDate: string; // DateTime
  endDate: string; // DateTime
}

export interface PartialEInvoiceV1 {
  // Core Parties
  supplier: Supplier;
  buyer: Buyer;
  // Invoice Specification
  invoiceCode: string;
}

export interface InvoiceLevelLineItemTaxesSubTotal {
  taxTypesExcludeExemption: string[]; // get code from TaxTypeService, Refer https://sdk.myinvois.hasil.gov.my/codes/tax-types/
  taxableAmount?: number; // 1000, this is treated as total excluded amount of tax when item is exempted
  taxAmount: number; // 10, in of case taxable amount is 1000 and tax rate is 10%

  taxExemptionReasons?: string[];
}

export interface LegalMonetaryTotal {
  excludingTax: number;
  includingTax: number;
  payableAmount: number;
  netAmount?: number;
  discountValue?: number;
  feeAmount?: number;
  payableRoundingAmount?: number;
}

export interface InvoiceLevelLineItemTaxes {
  totalTaxAmount: number;
  taxSubtotals: InvoiceLevelLineItemTaxesSubTotal[];
}

export interface AdditionalDocumentReference {
  id: string;
  type?: AdditionalDocumentReferenceType;
  description?: string;
}

export interface InvoiceDateTime {
  date: string; // DateTime
  time: string; // DateTime
}

export interface EInvoiceV1 extends PartialEInvoiceV1 {
  // Core Parties
  deliveryDetails?: DeliveryDetails;

  // Invoice Specification
  invoiceDateTime: InvoiceDateTime;
  issuerDigitalSignature?: string; // TODO: Pending Digital Signature implementation, Refer https://sdk.myinvois.hasil.gov.my/signature/ , and sample https://sdk.myinvois.hasil.gov.my/files/sample-ul-invoice-2.1-signed.min.json
  foreignCurrency?: ForeignCurrency;
  billingPeriod?: BillingPeriod;

  // Line Items
  lineItems: LineItem[];

  payment?: Payment;

  prePayment?: Prepayment;

  billingReferenceNumber?: string; // E12345678912, Supplier’s internal billing reference number to facilitate payment from Buyer

  // Should get these values from calculation, after validator get the values, and assign them manually into this E-Invoice Interface object. These field should use various field value above for calculation.
  // Derivable values
  legalMonetaryTotal: LegalMonetaryTotal;

  // Derivable values
  invoiceLevelLineItemTaxes: InvoiceLevelLineItemTaxes;

  invoiceLevelAllowanceCharge?: InvoiceLevelAllowanceCharge;

  // Mandatory where applicable
  additionalDocumentReference?: AdditionalDocumentReference[];
}

export interface DocumentSubmission {
  id: number;
  company_id: number;
  user_id: number;
  submission_uid?: string;
  total_documents: number;
  created_at: string;
  updated_at: string;
  user: User;
  company: Company;
  submitted_documents: SubmittedDocument[];
}

export interface User {
  id: number;
  name: string;
  email: string;
}

export type SubmittedDocument = {
  id: number;
  document_submission_id: number;
  order_id: number | null;
  company_id: number;
  user_id: number;
  code: string;
  uuid: string | null;
  status: 'Submitted' | 'Cancelled' | 'Valid' | 'Invalid';
  fail_reason: string;
  fail_details: Array<{
    code: string;
    message: string;
    field?: string;
  }>;
  document_details: EInvoiceV1;
  type: 'Invoice';
  created_at: string;
  updated_at: string;
  submission?: DocumentSubmission;
  order: Order;
  company: Company;
  user: User;
};

export type Order = {
  id: number;
  company_id: number;
  user_id: number;
  is_submitted_to_lhdn: boolean;
  invoice_code: string;
  buyer: Buyer;
  supplier: Supplier;
  delivery_details: DeliveryDetails;
  invoice_date_time: InvoiceDateTime;
  foreign_currency: ForeignCurrency;
  billing_period: BillingPeriod;
  line_items: LineItem[];
  payment: Payment;
  prePayment: Prepayment;
  billing_reference_number: string;
  legal_monetary_total: LegalMonetaryTotal;
  invoice_level_line_item_taxes: InvoiceLevelLineItemTaxes;
  invoice_level_allowance_charge: InvoiceLevelAllowanceCharge;
  additional_document_reference: AdditionalDocumentReference[];
  is_consolidate: boolean;
  is_ready: boolean;
  external_id: string;
  created_at: string; // DateTime
  updated_at: string; // DateTime
  status: 'Draft' | 'Pending' | 'Submitted';
  company: Company;
  user: User;
  submitted_documents: SubmittedDocument[];
};

export type Company = {
  id: number;
  user_id: number;
  client_id: string;
  client_secret: string;
  scope: string | null;
  access_token: string | null;
  token_expires_in: number | null;
  token_expires_at: string; // date
  name: string; // could be company name or individual name
  tin_code: string; // e.g C25845632020 (in case of taxpayer identified by TIN only)
  registration_number: string; // eg.************, when combine with tin code will be IG12345678912:************ (in case of taxpayer identified by TIN and has a company ROB)
  registration_type: RegistrationType;
  sst_registration_number: string;
  tourism_tax_registration_number: string;
  business_activity_description: string;
  msic_code: string;
  country: string;
  state: string;
  zip_code: string;
  city: string;
  address: string;
  phone: string;
  email: string;
  bank_account: string;
  exporter_certified_number: string; // only for exporting companies
  created_at: string; // DateTime;
  updated_at: string; // DateTime;
  // user: User;
  orders: Order[];
  submittedDocuments?: SubmittedDocument[];
};
